package cn.harmonycloud.knowledge.service.impl;

import cn.harmonycloud.resp.ResultEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;

import java.time.LocalDateTime;

import cn.harmonycloud.knowledge.mapper.CategoriesDocumentMapper;
import cn.harmonycloud.knowledge.mapper.CategoriesMapper;
import cn.harmonycloud.knowledge.service.*;
import cn.harmonycloud.model.dto.CategoryDocmentDTO;
import cn.harmonycloud.model.entity.*;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName CategoriesDocumentServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2025/5/14 15:30
 **/
@Service
public class CategoriesDocumentServiceImpl extends ServiceImpl<CategoriesDocumentMapper, CategoriesDocument> implements ICategoriesDocumentService {

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private IDocumentTagsService documentTagsService;
    @Autowired
    private ITagsService tagsService;
    @Autowired
    private ISpaceService spaceService;
    @Autowired
    private CategoriesMapper categoriesMapper;

    private static final int DOWNLOAD_EXPIRES = 5 * 60 * 1000;

    @Value("${knowledge.frameworkUrl}")
    String frameworkUrl;
    public static String URL_PRE = "/api/doc/wopi/files/";

    @Override
    public Page<Document> listByCategoriesId(Long categoriesId, String keywords, Long projectId, Long pageNo, Long pageSize) {
        Page<Document> page = new Page<>(pageNo, pageSize);
        List<CategoriesDocument> list = list(Wrappers.<CategoriesDocument>lambdaQuery()
                .eq(Objects.nonNull(categoriesId), CategoriesDocument::getCategoriesId, categoriesId).eq(CategoriesDocument::getIsRelease, 1));
        Long spaceId = null;
        if (Objects.nonNull(projectId)) {
            Space space = spaceService.getByProject(projectId);
            if (Objects.isNull(space)) return page;
            spaceId = space.getId();
        }
        if (CollectionUtils.isEmpty(list)) return page;

        // 获取文档ID列表
        List<Long> documentIds = list.stream().map(CategoriesDocument::getDocumentId).collect(Collectors.toList());

        // 如果有关键词搜索，需要进行多字段模糊搜索
        if (StringUtils.isNotEmpty(keywords)) {
            documentIds = getDocumentIdsByKeywords(keywords, documentIds, spaceId);
            if (CollectionUtils.isEmpty(documentIds)) return page;
        }

        // 分页查询文档
        Page<Document> documentPage = documentService.page(page, Wrappers.<Document>lambdaQuery()
                .eq(Objects.nonNull(spaceId), Document::getSpaceId, spaceId)
                .in(Document::getId, documentIds));
        List<Document> documentList = fillDTOs(documentPage.getRecords(), list);
        documentList = documentList.stream().sorted(Comparator.comparing(Document::getSort)).collect(Collectors.toList());
        documentPage.setRecords(documentList);
        return documentPage;
    }

    @Override
    public Map<Long, List<CategoriesDocument>> listByCategoriesIds(List<Long> categoriesIds) {
        List<CategoriesDocument> list = list(Wrappers.<CategoriesDocument>lambdaQuery().in(CollectionUtils.isNotEmpty(categoriesIds), CategoriesDocument::getCategoriesId, categoriesIds));
        if (CollectionUtils.isEmpty(list)) return null;
        return list.stream().collect(Collectors.groupingBy(CategoriesDocument::getCategoriesId));
    }

    @Override
    public Document detail(Long id) {
        Document document = documentService.getById(id);
        if (Objects.nonNull(document)) {
            List<Document> documents = fillDTOs(Arrays.asList(document), null);
            Document result = documents.get(0);
            Space space = spaceService.getByProject(-1L);
            if (Objects.nonNull(space)) {
                try {
                    //  String url = fileTemplate.getObjectURL(space.getBucket(), document.getUniqueKey(), DOWNLOAD_EXPIRES);
                    String url = frameworkUrl + URL_PRE + result.getId();
                    if (Objects.nonNull(url)) result.setUrl(url.split("\\?")[0]);
                } catch (Exception e) {
                    log.error("get minio url fail!");
                }
            }
            List<CategoriesDocument> list = list(Wrappers.<CategoriesDocument>lambdaQuery().eq(CategoriesDocument::getDocumentId, id));
            if (CollectionUtils.isNotEmpty(list))
                return result.setCategories(categoriesMapper.selectBatchIds(list.stream().map(CategoriesDocument::getCategoriesId).collect(Collectors.toList())));
            return result;
        }
        return document;
    }

    @Override
    public Boolean syncDocs(Long spaceId) {
        Space space = spaceService.getByProject(-1L);
        if (Objects.isNull(space)) {
            return true;
        }
        List<Document> documentList = documentService.list(Wrappers.<Document>lambdaQuery().eq(Objects.nonNull(spaceId), Document::getSpaceId, spaceId));
        if (CollectionUtils.isEmpty(documentList)) {
            return true;
        }
        documentList.forEach(document -> {
            document.setSpaceId(space.getId());
            documentService.save(document);
        });
        return true;
    }

    private List<Document> fillDTOs(List<Document> documentList, List<CategoriesDocument> categoriesDocumentList) {
        if (CollectionUtils.isEmpty(documentList)) {
            return documentList;
        }
        List<Long> docIds = documentList.stream().map(Document::getId).collect(Collectors.toList());
        List<DocumentTags> documentTagsList = documentTagsService.list(Wrappers.<DocumentTags>lambdaQuery().in(DocumentTags::getDocumentId, docIds));
        if (CollectionUtils.isEmpty(documentTagsList)) {
            return documentList;
        }
        Map<Long, List<DocumentTags>> documentIdAndTagsList = documentTagsList.stream().collect(Collectors.groupingBy(DocumentTags::getDocumentId));
        List<Long> tagIdList = documentTagsList.stream().map(DocumentTags::getTagsId).collect(Collectors.toList());
        List<Tags> tagsList = tagsService.listByIds(tagIdList);
        if (CollectionUtils.isEmpty(tagsList)) {
            return documentList;
        }

        documentList.forEach(document -> {
            List<DocumentTags> dts = documentIdAndTagsList.get(document.getId());
            if (CollectionUtils.isNotEmpty(dts)) {
                List<Long> tagIds = dts.stream().map(DocumentTags::getTagsId).collect(Collectors.toList());
                List<Tags> tags = tagsList.stream().filter(s -> tagIds.contains(s.getId())).collect(Collectors.toList());
                //填充标签
                document.setTags(tags);
                //填充文档版本、是否发布
                if (CollectionUtils.isNotEmpty(categoriesDocumentList) && categoriesDocumentList.stream().filter(s -> s.getDocumentId().equals(document.getId())).findFirst().isPresent()) {
                    CategoriesDocument categoriesDocument = categoriesDocumentList.stream().filter(s -> s.getDocumentId().equals(document.getId())).findFirst().get();
                    document.setSort(categoriesDocument.getSort());
                    document.setDocVersion(categoriesDocument.getDocVersion());
                    document.setIsRelease(categoriesDocument.getIsRelease());
                }
            }
        });
        return documentList;
    }

    @Override
    public Page<Document> listByProjectId(Long categoriesId, Long subCategoriesId, String keywords, Long projectId,
                                          String alias, String tagIds, String updateBy, Long pageNo, Long pageSize) {
        Page<Document> page = new Page<>(pageNo, pageSize);
        Long spaceId = null;
        if (Objects.nonNull(projectId)) {
            Space space = spaceService.getByProject(projectId);
            if (Objects.isNull(space)) return page;
            spaceId = space.getId();
        }
        // 构建查询条件
        LambdaQueryWrapper<Document> queryWrapper = Wrappers.<Document>lambdaQuery()
                .like(StringUtils.isNotEmpty(keywords), Document::getName, keywords)
                .like(StringUtils.isNotEmpty(alias), Document::getAlias, alias)
                .like(StringUtils.isNotBlank(updateBy), Document::getUpdateBy, updateBy)
                .eq(Objects.nonNull(spaceId), Document::getSpaceId, spaceId)
                .orderByDesc(Document::getUpdateTime);

        // 如果传了分类ID，则按分类过滤；否则查询所有文档
        if (Objects.nonNull(categoriesId) || Objects.nonNull(subCategoriesId)) {
            List<Long> documentIdList = getDocumentIdsByCategory(categoriesId, subCategoriesId);
            if (CollectionUtils.isEmpty(documentIdList)) {
                return page;
            }
            queryWrapper.in(Document::getId, documentIdList);
        }
        // 如果传了标签ID，则按标签过滤
        if (StringUtils.isNotBlank(tagIds)) {
            List<Long> tagIdList = Arrays.asList(tagIds.split(",")).stream().map(Long::valueOf).collect(Collectors.toList());
            List<DocumentTags> documentTagsList = documentTagsService.list(Wrappers.<DocumentTags>lambdaQuery()
                    .in(DocumentTags::getTagsId, tagIdList));
            if (CollectionUtils.isEmpty(documentTagsList)) {
                return page;
            }
            List<Long> documentIdList = documentTagsList.stream().map(DocumentTags::getDocumentId).collect(Collectors.toList());
            queryWrapper.in(Document::getId, documentIdList);
        }
        Page<Document> documentPage = documentService.page(page, queryWrapper);

        List<Document> documentList = fillDTOs(documentPage.getRecords(), null);
        // 优化排序：先按sort排序，再按创建时间倒序排序
        documentList = documentList.stream()
                //按实更新时间排序，后台页面暂无排序值设置
                //        .sorted(Comparator.comparing(Document::getSort)
                //               .thenComparing(Document::getUpdateTime, Comparator.reverseOrder()))
                .sorted(Comparator.comparing(Document::getUpdateTime, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        //补充分类信息 - 批量查询优化
        fillCategoriesInfoBatch(documentList);
        documentPage.setRecords(documentList);
        return documentPage;
    }

    /**
     * 根据分类ID获取文档ID列表
     *
     * @param categoriesId    一级分类ID
     * @param subCategoriesId 二级分类ID
     * @return 文档ID列表
     */
    private List<Long> getDocumentIdsByCategory(Long categoriesId, Long subCategoriesId) {
        if (Objects.nonNull(subCategoriesId)) {
            // 查询指定二级分类下的文档
            List<CategoriesDocument> list = list(Wrappers.<CategoriesDocument>lambdaQuery()
                    .eq(CategoriesDocument::getCategoriesId, subCategoriesId));
            return CollectionUtils.isEmpty(list) ? Collections.emptyList() :
                    list.stream().map(CategoriesDocument::getDocumentId).collect(Collectors.toList());
        } else if (Objects.nonNull(categoriesId)) {
            // 查询一级分类下所有二级分类的文档
            List<Categories> subCategoriesList = categoriesMapper.selectList(
                    Wrappers.<Categories>lambdaQuery().eq(Categories::getParentId, categoriesId));
            if (CollectionUtils.isEmpty(subCategoriesList)) {
                return Collections.emptyList();
            }

            List<Long> subCategoryIds = subCategoriesList.stream()
                    .map(Categories::getId).collect(Collectors.toList());
            List<CategoriesDocument> list = list(Wrappers.<CategoriesDocument>lambdaQuery()
                    .in(CategoriesDocument::getCategoriesId, subCategoryIds));
            return CollectionUtils.isEmpty(list) ? Collections.emptyList() :
                    list.stream().map(CategoriesDocument::getDocumentId).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 批量填充文档的分类信息，避免N+1查询问题
     *
     * @param documentList 文档列表
     */
    private void fillCategoriesInfoBatch(List<Document> documentList) {
        if (CollectionUtils.isEmpty(documentList)) {
            return;
        }

        // 1. 批量查询文档分类关系
        List<Long> documentIds = documentList.stream().map(Document::getId).collect(Collectors.toList());
        List<CategoriesDocument> categoriesDocuments = list(Wrappers.<CategoriesDocument>lambdaQuery()
                .in(CategoriesDocument::getDocumentId, documentIds));

        if (CollectionUtils.isEmpty(categoriesDocuments)) {
            return;
        }

        // 2. 批量查询所有相关的分类
        Set<Long> categoryIds = categoriesDocuments.stream()
                .map(CategoriesDocument::getCategoriesId)
                .collect(Collectors.toSet());

        List<Categories> allCategories = categoriesMapper.selectBatchIds(categoryIds);
        if (CollectionUtils.isEmpty(allCategories)) {
            return;
        }

        // 3. 获取所有父分类ID并批量查询（排除parentId=0的根级分类）
        Set<Long> parentCategoryIds = allCategories.stream()
                .map(Categories::getParentId)
                .filter(Objects::nonNull)
                .filter(parentId -> !parentId.equals(0L))
                .collect(Collectors.toSet());

        List<Categories> parentCategories = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(parentCategoryIds)) {
            parentCategories = categoriesMapper.selectBatchIds(parentCategoryIds);
        }

        // 4. 构建映射关系
        Map<Long, CategoriesDocument> docToCategoryMap = categoriesDocuments.stream()
                .collect(Collectors.toMap(CategoriesDocument::getDocumentId, Function.identity(), (v1, v2) -> v1));

        Map<Long, Categories> categoryMap = allCategories.stream()
                .collect(Collectors.toMap(Categories::getId, Function.identity()));

        Map<Long, Categories> parentCategoryMap = parentCategories.stream()
                .collect(Collectors.toMap(Categories::getId, Function.identity()));

        // 5. 填充文档分类信息
        documentList.forEach(document -> {
            CategoriesDocument categoriesDocument = docToCategoryMap.get(document.getId());
            if (Objects.nonNull(categoriesDocument)) {
                Categories secondCategories = categoryMap.get(categoriesDocument.getCategoriesId());
                if (Objects.nonNull(secondCategories)) {
                    document.setSecondCategories(secondCategories);

                    Categories firstCategories = parentCategoryMap.get(secondCategories.getParentId());
                    if (Objects.nonNull(firstCategories)) {
                        document.setFirstCategories(firstCategories);
                    }
                }
            }
        });
    }

    @Override
    public Boolean saveBatchCus(CategoryDocmentDTO dto) {

        if (StringUtils.isBlank(dto.getCategoriesIds())) {
            return false;
        }

        // 别名不能重复
        if (StringUtils.isNotBlank(dto.getAlias())) {
            handleDuplicateAlias(dto.getAlias(), dto.getCategoriesIds(), dto.getDocumentId());
        }

        Long documentId = dto.getDocumentId();
        List<String> categoriesIds = Arrays.asList(dto.getCategoriesIds().split(","));
        CategoriesDocument categoriesDocument = new CategoriesDocument();
        categoriesDocument.setDocumentId(documentId);
        //目前tasp没有发布操作，上传文件就默认发布
        categoriesDocument.setIsRelease(1);
        categoriesDocument.setSort(dto.getSort());
        categoriesDocument.setDocVersion(dto.getDocVersion());
        if (CollectionUtils.isNotEmpty(dto.getTagIds())) {
            categoriesDocument.setTagIds(String.join(",", dto.getTagIds()));
            documentTagsService.remove(Wrappers.<DocumentTags>lambdaQuery().eq(DocumentTags::getDocumentId, documentId));
            dto.getTagIds().forEach(tagId -> {
                DocumentTags documentTags = new DocumentTags();
                documentTags.setDocumentId(documentId);
                documentTags.setTagsId(Long.valueOf(tagId));
                documentTagsService.save(documentTags);
            });

        }
        categoriesDocument.setIsRelease(categoriesDocument.getIsRelease());
        categoriesIds.forEach(categoriesId -> {
            categoriesDocument.setCategoriesId(Long.valueOf(categoriesId));
            save(categoriesDocument);
            Document document = documentService.getById(dto.getDocumentId());
            if (StringUtils.isNotBlank(dto.getAlias()) && null != document) {
                document.setAlias(dto.getAlias());
                document.setKeyWords(dto.getKeyWords());
                document.setDescription(dto.getDescription());
                documentService.updateById(document);
            }
        });

        return true;
    }

    //文档编辑：标签、描述、发布、版本、是否链接、链接地址、关键词；
    @Override
    public Boolean updateDocument(CategoryDocmentDTO categoryDocmentDTO) {
        Long documentId = categoryDocmentDTO.getId();

        //分类更新需要先删除再更新
        if (StringUtils.isNotBlank(categoryDocmentDTO.getCategoriesIds())) {
            remove(Wrappers.<CategoriesDocument>lambdaQuery().eq(CategoriesDocument::getDocumentId, documentId));
            CategoriesDocument categoriesDocument = new CategoriesDocument();
            BeanUtils.copyProperties(categoryDocmentDTO, categoriesDocument);
            categoriesDocument.setCategoriesId(Long.valueOf(categoryDocmentDTO.getCategoriesIds()));
            categoriesDocument.setDocumentId(documentId);
            categoriesDocument.setId(null);
            categoriesDocument.setIsRelease(1);
            save(categoriesDocument);
        }
        // 检查别名是否重复（排除当前文档）
        if (StringUtils.isNotBlank(categoryDocmentDTO.getAlias())) {
            checkAliasNotDuplicate(categoryDocmentDTO.getAlias(), categoryDocmentDTO.getCategoriesIds(), documentId);
        }
        Document doc = documentService.getById(documentId);
        doc.setAlias(categoryDocmentDTO.getAlias());
        doc.setKeyWords(categoryDocmentDTO.getKeyWords());
        doc.setSort(categoryDocmentDTO.getSort());
        doc.setDocVersion(categoryDocmentDTO.getDocVersion());
        doc.setDescription(categoryDocmentDTO.getDescription());
        documentService.updateById(doc);
        if (CollectionUtils.isNotEmpty(categoryDocmentDTO.getTagIds()) && null != categoryDocmentDTO.getTagIds()) {
            documentTagsService.remove(Wrappers.<DocumentTags>lambdaQuery().eq(DocumentTags::getDocumentId, documentId));
            categoryDocmentDTO.getTagIds().forEach(tagId -> {
                DocumentTags documentTags = new DocumentTags();
                documentTags.setDocumentId(documentId);
                documentTags.setTagsId(Long.valueOf(tagId));
                documentTagsService.save(documentTags);
            });
        }
        if (Objects.nonNull(categoryDocmentDTO.getIsRelease())) {
            update(Wrappers.<CategoriesDocument>lambdaUpdate().eq(CategoriesDocument::getDocumentId, documentId).set(CategoriesDocument::getIsRelease, categoryDocmentDTO.getIsRelease()));
        }
        if (StringUtils.isNotEmpty(categoryDocmentDTO.getDocVersion())) {
            update(Wrappers.<CategoriesDocument>lambdaUpdate().eq(CategoriesDocument::getDocumentId, documentId).set(CategoriesDocument::getDocVersion, categoryDocmentDTO.getDocVersion()));
        }
        return true;
    }

    /**
     * 处理重复别名：如果发现重复，删除指定的文档并报错
     *
     * @param alias         文档别名
     * @param categoriesIds 分类ID列表
     * @param documentId    要删除的文档ID
     */
    private void handleDuplicateAlias(String alias, String categoriesIds, Long documentId) {
        List<CategoriesDocument> duplicateDocuments = getCategoriesDocumentByAlias(alias, categoriesIds, null);

        if (CollectionUtils.isNotEmpty(duplicateDocuments)) {
            // 只删除请求中指定的documentId对应的文档
            if (Objects.nonNull(documentId)) {
                documentService.removeById(documentId);
            }

            // 删除后仍然报错
            ResultEnum.DOCUMENT_ALIAS_IS_EXIST.isFalse(true);
        }
    }

    /**
     * 检查别名是否重复（更新时排除当前文档）
     *
     * @param alias             文档别名
     * @param categoriesIds     分类ID列表
     * @param excludeDocumentId 要排除的文档ID（更新时的当前文档）
     */
    private void checkAliasNotDuplicate(String alias, String categoriesIds, Long excludeDocumentId) {
        List<CategoriesDocument> categoriesDocumentByAlias = getCategoriesDocumentByAlias(alias, categoriesIds, excludeDocumentId);
        ResultEnum.DOCUMENT_ALIAS_IS_EXIST.isFalse(CollectionUtils.isNotEmpty(categoriesDocumentByAlias));
    }

    /**
     * 根据文档别名和栏目ID查询文档关联（支持排除指定文档）
     *
     * @param alias             文档别名
     * @param categoriesIds     栏目ID
     * @param excludeDocumentId 要排除的文档ID（可为null）
     * @return
     */
    private List<CategoriesDocument> getCategoriesDocumentByAlias(String alias, String categoriesIds, Long excludeDocumentId) {
        LambdaQueryWrapper<Document> documentQuery = Wrappers.<Document>lambdaQuery()
                .eq(Document::getAlias, alias);

        // 如果是更新操作，排除当前文档
        if (Objects.nonNull(excludeDocumentId)) {
            documentQuery.ne(Document::getId, excludeDocumentId);
        }

        List<Document> list = documentService.list(documentQuery);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        Set<Long> docIds = list.stream().map(Document::getId).collect(Collectors.toSet());
        return list(Wrappers.<CategoriesDocument>lambdaQuery()
                .in(StringUtils.isNotBlank(categoriesIds), CategoriesDocument::getCategoriesId, categoriesIds.split(","))
                .in(CategoriesDocument::getDocumentId, docIds));
    }

    /**
     * 根据文档别名和栏目ID查询文档关联（兼容旧方法）
     *
     * @param alias         文档别名
     * @param categoriesIds 栏目ID
     * @return
     */
    private List<CategoriesDocument> getCategoriesDocumentByAlias(String alias, String categoriesIds) {
        return getCategoriesDocumentByAlias(alias, categoriesIds, null);
    }

    /**
     * 根据关键词搜索文档ID列表，支持模糊搜索名称、别名、标签、关键词
     *
     * @param keywords    搜索关键词
     * @param documentIds 原始文档ID列表
     * @param spaceId     空间ID
     * @return 匹配的文档ID列表
     */
    private List<Long> getDocumentIdsByKeywords(String keywords, List<Long> documentIds, Long spaceId) {
        Set<Long> matchedDocumentIds = new HashSet<>();

        // 1. 搜索文档的名称、别名、关键词字段
        List<Document> documentsMatchedByFields = documentService.list(Wrappers.<Document>lambdaQuery()
                .and(wrapper -> wrapper
                        .like(Document::getName, keywords)
                        .or()
                        .like(Document::getAlias, keywords)
                        .or()
                        .like(Document::getKeyWords, keywords)
                )
                .eq(Objects.nonNull(spaceId), Document::getSpaceId, spaceId)
                .in(Document::getId, documentIds));

        if (CollectionUtils.isNotEmpty(documentsMatchedByFields)) {
            matchedDocumentIds.addAll(documentsMatchedByFields.stream()
                    .map(Document::getId)
                    .collect(Collectors.toSet()));
        }

        // 2. 搜索标签名称
        List<Tags> matchedTags = tagsService.list(Wrappers.<Tags>lambdaQuery()
                .like(Tags::getName, keywords));

        if (CollectionUtils.isNotEmpty(matchedTags)) {
            List<Long> tagIds = matchedTags.stream().map(Tags::getId).collect(Collectors.toList());

            // 查找包含这些标签的文档
            List<DocumentTags> documentTagsList = documentTagsService.list(Wrappers.<DocumentTags>lambdaQuery()
                    .in(DocumentTags::getTagsId, tagIds)
                    .in(DocumentTags::getDocumentId, documentIds));

            if (CollectionUtils.isNotEmpty(documentTagsList)) {
                matchedDocumentIds.addAll(documentTagsList.stream()
                        .map(DocumentTags::getDocumentId)
                        .collect(Collectors.toSet()));
            }
        }

        return new ArrayList<>(matchedDocumentIds);
    }

}